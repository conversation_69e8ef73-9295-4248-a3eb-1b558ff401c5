/**
 * إصلاحات شاملة للأخطاء - SKILLS WORLD ACADEMY
 * Comprehensive Error Fixes for Console Errors
 */

// إصلاح أخطاء console.log غير المعرفة
const safeConsole = {
  log: (...args) => {
    if (typeof console !== 'undefined' && console.log) {
      console.log(...args);
    }
  },
  error: (...args) => {
    if (typeof console !== 'undefined' && console.error) {
      console.error(...args);
    }
  },
  warn: (...args) => {
    if (typeof console !== 'undefined' && console.warn) {
      console.warn(...args);
    }
  },
  info: (...args) => {
    if (typeof console !== 'undefined' && console.info) {
      console.info(...args);
    }
  }
};

// إصلاح أخطاء المتغيرات غير المعرفة
const safeGlobals = {
  window: typeof window !== 'undefined' ? window : {},
  document: typeof document !== 'undefined' ? document : {},
  localStorage: typeof localStorage !== 'undefined' ? localStorage : {
    getItem: () => null,
    setItem: () => {},
    removeItem: () => {},
    clear: () => {}
  },
  sessionStorage: typeof sessionStorage !== 'undefined' ? sessionStorage : {
    getItem: () => null,
    setItem: () => {},
    removeItem: () => {},
    clear: () => {}
  }
};

// إصلاح أخطاء الدوال المفقودة
const safeFunctions = {
  setTimeout: typeof setTimeout !== 'undefined' ? setTimeout : (fn, delay) => {
    safeConsole.warn('setTimeout غير متاح');
    return null;
  },
  setInterval: typeof setInterval !== 'undefined' ? setInterval : (fn, delay) => {
    safeConsole.warn('setInterval غير متاح');
    return null;
  },
  clearTimeout: typeof clearTimeout !== 'undefined' ? clearTimeout : (id) => {
    safeConsole.warn('clearTimeout غير متاح');
  },
  clearInterval: typeof clearInterval !== 'undefined' ? clearInterval : (id) => {
    safeConsole.warn('clearInterval غير متاح');
  }
};

// إصلاح أخطاء Promise
const safePromise = {
  resolve: (value) => {
    if (typeof Promise !== 'undefined') {
      return Promise.resolve(value);
    }
    return {
      then: (callback) => callback(value),
      catch: () => {}
    };
  },
  reject: (error) => {
    if (typeof Promise !== 'undefined') {
      return Promise.reject(error);
    }
    return {
      then: () => {},
      catch: (callback) => callback(error)
    };
  }
};

// إصلاح أخطاء fetch
const safeFetch = async (url, options = {}) => {
  try {
    if (typeof fetch !== 'undefined') {
      return await fetch(url, options);
    } else {
      throw new Error('fetch غير متاح');
    }
  } catch (error) {
    safeConsole.error('خطأ في safeFetch:', error);
    return {
      ok: false,
      status: 500,
      statusText: 'Internal Error',
      json: () => safePromise.resolve({}),
      text: () => safePromise.resolve(''),
      blob: () => safePromise.resolve(new Blob())
    };
  }
};

// إصلاح أخطاء addEventListener
const safeEventListener = {
  add: (element, event, handler, options) => {
    try {
      if (element && typeof element.addEventListener === 'function') {
        element.addEventListener(event, handler, options);
      } else {
        safeConsole.warn('addEventListener غير متاح للعنصر:', element);
      }
    } catch (error) {
      safeConsole.error('خطأ في إضافة مستمع الحدث:', error);
    }
  },
  remove: (element, event, handler, options) => {
    try {
      if (element && typeof element.removeEventListener === 'function') {
        element.removeEventListener(event, handler, options);
      } else {
        safeConsole.warn('removeEventListener غير متاح للعنصر:', element);
      }
    } catch (error) {
      safeConsole.error('خطأ في إزالة مستمع الحدث:', error);
    }
  }
};

// إصلاح أخطاء JSON
const safeJSON = {
  parse: (str, defaultValue = null) => {
    try {
      return JSON.parse(str);
    } catch (error) {
      safeConsole.warn('خطأ في تحليل JSON:', error);
      return defaultValue;
    }
  },
  stringify: (obj, defaultValue = '{}') => {
    try {
      return JSON.stringify(obj);
    } catch (error) {
      safeConsole.warn('خطأ في تحويل JSON:', error);
      return defaultValue;
    }
  }
};

// إصلاح أخطاء Date
const safeDate = {
  now: () => {
    try {
      return Date.now();
    } catch (error) {
      safeConsole.warn('خطأ في Date.now:', error);
      return 0;
    }
  },
  create: (value) => {
    try {
      return new Date(value);
    } catch (error) {
      safeConsole.warn('خطأ في إنشاء Date:', error);
      return new Date();
    }
  }
};

// إصلاح أخطاء Math
const safeMath = {
  random: () => {
    try {
      return Math.random();
    } catch (error) {
      safeConsole.warn('خطأ في Math.random:', error);
      return 0.5;
    }
  },
  floor: (num) => {
    try {
      return Math.floor(num);
    } catch (error) {
      safeConsole.warn('خطأ في Math.floor:', error);
      return 0;
    }
  }
};

// دالة تطبيق جميع الإصلاحات
export const applyErrorFixes = () => {
  try {
    // تطبيق الإصلاحات على النطاق العام
    if (typeof window !== 'undefined') {
      window.safeConsole = safeConsole;
      window.safeGlobals = safeGlobals;
      window.safeFunctions = safeFunctions;
      window.safePromise = safePromise;
      window.safeFetch = safeFetch;
      window.safeEventListener = safeEventListener;
      window.safeJSON = safeJSON;
      window.safeDate = safeDate;
      window.safeMath = safeMath;
    }

    safeConsole.log('✅ تم تطبيق جميع إصلاحات الأخطاء بنجاح');
    return true;
  } catch (error) {
    safeConsole.error('❌ خطأ في تطبيق إصلاحات الأخطاء:', error);
    return false;
  }
};

// تطبيق الإصلاحات تلقائياً عند تحميل الملف
applyErrorFixes();

// تصدير جميع الأدوات الآمنة
export {
  safeConsole,
  safeGlobals,
  safeFunctions,
  safePromise,
  safeFetch,
  safeEventListener,
  safeJSON,
  safeDate,
  safeMath
};

export default {
  applyErrorFixes,
  safeConsole,
  safeGlobals,
  safeFunctions,
  safePromise,
  safeFetch,
  safeEventListener,
  safeJSON,
  safeDate,
  safeMath
};
