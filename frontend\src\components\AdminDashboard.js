import React, { useState, useEffect } from 'react';
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  List,
  Typography,
  Divider,
  IconButton,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  Menu,
  MenuItem,
  Badge,
  Tooltip,
  useTheme,
  useMediaQuery,
  Container,
  Alert,
  CircularProgress,
  Chip
} from '@mui/material';
import {
  Menu as MenuIcon,
  Dashboard,
  School,
  VideoLibrary,
  Group,
  PersonAdd,
  Help,
  BarChart,
  Settings,
  Logout,
  Notifications,
  CheckCircle,
  Error as ErrorIcon,
  Sync,
  DeleteSweep,
  Add
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import toast from 'react-hot-toast';

// استيراد خدمة التكامل المختلطة Firebase + Supabase
import {
  hybridAuth,
  hybridCourses,
  hybridStudents,
  hybridEnrollments,
  hybridFAQs
} from '../services/hybridDatabaseService';
import { initializeSupabase } from '../supabase/config';

// استيراد مكونات لوحة التحكم الموجودة
import DashboardOverview from './admin/DashboardOverview';
import CourseManagement from './admin/CourseManagement';
import StudentManagement from './admin/StudentManagement';
import StudentEnrollmentManagement from './admin/StudentEnrollmentManagement';
import FAQManagement from './admin/FAQManagement';
import AdminProfile from './admin/AdminProfile';
import SystemSettings from './admin/SystemSettings';

const drawerWidth = 300;

const AdminDashboard = () => {
  const { user, logout } = useAuth();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // حالات المكون
  const [mobileOpen, setMobileOpen] = useState(false);
  const [selectedSection, setSelectedSection] = useState('dashboard');
  const [anchorEl, setAnchorEl] = useState(null);
  const [systemStatus, setSystemStatus] = useState('initializing');
  const [realtimeStatus, setRealtimeStatus] = useState('disconnected');
  const [adminData, setAdminData] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // قائمة عناصر القائمة الجانبية (تم تنظيفها من أدوات الاختبار)
  const menuItems = [
    {
      id: 'dashboard',
      label: 'لوحة التحكم الرئيسية',
      icon: <Dashboard />,
      color: '#2196F3',
      description: 'نظرة عامة على النظام'
    },
    {
      id: 'courses',
      label: 'إدارة الكورسات',
      icon: <VideoLibrary />,
      color: '#4CAF50',
      description: 'إضافة وإدارة الكورسات'
    },
    {
      id: 'students',
      label: 'إدارة الطلاب',
      icon: <Group />,
      color: '#FF9800',
      description: 'إضافة وإدارة الطلاب'
    },
    {
      id: 'enrollments',
      label: 'إدارة التسجيلات',
      icon: <PersonAdd />,
      color: '#9C27B0',
      description: 'تسجيل الطلاب في الكورسات'
    },
    {
      id: 'faqs',
      label: 'الأسئلة الشائعة',
      icon: <Help />,
      color: '#00BCD4',
      description: 'إدارة الأسئلة الشائعة'
    },
    {
      id: 'profile',
      label: 'الملف الشخصي',
      icon: <Settings />,
      color: '#607D8B',
      description: 'إعدادات الملف الشخصي'
    },
    {
      id: 'system-settings',
      label: 'إعدادات النظام',
      icon: <Settings />,
      color: '#795548',
      description: 'أدوات الاختبار والصيانة والتشخيص'
    }
  ];

  // تهيئة النظام عند تحميل المكون
  useEffect(() => {
    initializeSystem();
    return () => {
      // تنظيف المراقبات عند إلغاء تحميل المكون
      console.log('🧹 تنظيف مراقبات النظام...');
    };
  }, []);

  // تهيئة النظام المختلط
  const initializeSystem = async () => {
    try {
      setLoading(true);
      setSystemStatus('initializing');

      console.log('🚀 بدء تهيئة النظام المختلط Firebase + Supabase...');

      // تهيئة Supabase
      await initializeSupabase();

      // التحقق من حالة المصادقة
      const currentUser = hybridAuth.getCurrentUser();
      if (currentUser) {
        console.log('👤 المستخدم مسجل دخول:', currentUser.email);
      }

      setSystemStatus('connected');

      // بدء مراقبة البيانات الفورية
      startRealtimeSync();

      toast.success('تم تهيئة النظام المختلط بنجاح');
      console.log('✅ تم تهيئة النظام المختلط بنجاح');
    } catch (error) {
      console.error('❌ خطأ في تهيئة النظام:', error);
      setSystemStatus('error');
      setError('فشل في تهيئة النظام: ' + error.message);
      toast.error('فشل في تهيئة النظام');
    } finally {
      setLoading(false);
    }
  };

  // بدء المزامنة الفورية المختلطة
  const startRealtimeSync = () => {
    try {
      setRealtimeStatus('connecting');

      // مراقبة الكورسات
      const coursesUnsubscribe = hybridCourses.watchCourses((courses) => {
        setAdminData(prev => ({ ...prev, courses }));
        console.log('📚 تحديث الكورسات:', courses.length);
      });

      // مراقبة الطلاب
      const studentsUnsubscribe = hybridStudents.watchStudents((students) => {
        setAdminData(prev => ({ ...prev, students }));
        console.log('👨‍🎓 تحديث الطلاب:', students.length);
      });

      // مراقبة التسجيلات
      const enrollmentsUnsubscribe = hybridEnrollments.watchEnrollments((enrollments) => {
        setAdminData(prev => ({ ...prev, enrollments }));
        console.log('📝 تحديث التسجيلات:', enrollments.length);
      });

      // مراقبة الأسئلة الشائعة
      const faqsUnsubscribe = hybridFAQs.watchFAQs((faqs) => {
        setAdminData(prev => ({ ...prev, faqs }));
        console.log('❓ تحديث الأسئلة الشائعة:', faqs.length);
      });

      setRealtimeStatus('connected');
      console.log('🔄 تم بدء المزامنة الفورية المختلطة');

      // حفظ دوال إلغاء الاشتراك للتنظيف لاحقاً
      window.hybridUnsubscribes = [
        coursesUnsubscribe,
        studentsUnsubscribe,
        enrollmentsUnsubscribe,
        faqsUnsubscribe
      ];
    } catch (error) {
      console.error('❌ خطأ في المزامنة الفورية:', error);
      setRealtimeStatus('error');
    }
  };

  // التعامل مع تبديل القائمة الجانبية
  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  // التعامل مع تغيير القسم
  const handleSectionChange = (sectionId) => {
    setSelectedSection(sectionId);
    if (isMobile) {
      setMobileOpen(false);
    }
  };

  // التعامل مع قائمة المستخدم
  const handleUserMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleUserMenuClose = () => {
    setAnchorEl(null);
  };

  // تسجيل الخروج
  const handleLogout = async () => {
    try {
      console.log('🚪 بدء عملية تسجيل الخروج...');

      // إيقاف جميع المراقبات
      if (window.hybridUnsubscribes) {
        window.hybridUnsubscribes.forEach(unsubscribe => unsubscribe());
        window.hybridUnsubscribes = [];
      }

      // تنظيف جميع البيانات المحلية
      localStorage.clear();
      sessionStorage.clear();

      // تسجيل خروج من النظام المختلط
      await hybridAuth.signOut();

      // تسجيل خروج من AuthContext
      await logout();

      console.log('✅ تم تسجيل الخروج بنجاح');
      toast.success('تم تسجيل الخروج بنجاح - جاري التحويل...');

      // إعادة التوجيه الفورية إلى صفحة تسجيل الدخول
      setTimeout(() => {
        // استخدام replace لمنع العودة للخلف وإجبار إعادة تحميل الصفحة
        window.location.replace('/login');
      }, 800);

    } catch (error) {
      console.error('❌ خطأ في تسجيل الخروج:', error);
      toast.error('خطأ في تسجيل الخروج - جاري التحويل...');

      // حتى في حالة الخطأ، قم بتنظيف البيانات والتحويل
      localStorage.clear();
      sessionStorage.clear();

      setTimeout(() => {
        window.location.replace('/login');
      }, 1000);
    }
  };

  // عرض المحتوى حسب القسم المحدد
  const renderContent = () => {
    if (loading) {
      return (
        <Box display="flex" justifyContent="center" alignItems="center" height="400px">
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ ml: 2 }}>
            جاري تهيئة النظام...
          </Typography>
        </Box>
      );
    }

    if (systemStatus === 'error') {
      return (
        <Alert severity="error" sx={{ m: 2 }}>
          <Typography variant="h6">خطأ في النظام</Typography>
          <Typography>{error}</Typography>
        </Alert>
      );
    }

    switch (selectedSection) {
      case 'dashboard':
        return <DashboardOverview adminData={adminData} />;
      case 'courses':
        return <CourseManagement />;
      case 'students':
        return <StudentManagement />;
      case 'enrollments':
        return <StudentEnrollmentManagement />;
      case 'faqs':
        return <FAQManagement />;
      case 'profile':
        return <AdminProfile />;
      case 'system-settings':
        return <SystemSettings />;
      default:
        return <DashboardOverview adminData={adminData} />;
    }
  };

  // مؤشر حالة الاتصال
  const getStatusColor = (status) => {
    switch (status) {
      case 'connected': return '#4CAF50';
      case 'connecting': return '#FF9800';
      case 'error': return '#F44336';
      default: return '#9E9E9E';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'connected': return 'متصل';
      case 'connecting': return 'جاري الاتصال';
      case 'error': return 'خطأ في الاتصال';
      default: return 'غير متصل';
    }
  };

  // القائمة الجانبية
  const drawer = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* رأس القائمة */}
      <Box
        sx={{
          p: 3,
          background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 100%)',
          color: 'white',
          textAlign: 'center'
        }}
      >
        <School sx={{ fontSize: '2.5rem', mb: 1, color: '#FFD700' }} />
        <Typography variant="h6" sx={{ fontWeight: 'bold', fontSize: '1.1rem' }}>
          SKILLS WORLD ACADEMY
        </Typography>
        <Typography variant="body2" sx={{ opacity: 0.9, mt: 0.5 }}>
          لوحة تحكم المدير - الإنتاج
        </Typography>

        {/* مؤشر حالة الاتصال */}
        <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <Chip
            icon={realtimeStatus === 'connected' ? <CheckCircle /> :
                  realtimeStatus === 'error' ? <ErrorIcon /> : <Sync />}
            label={getStatusText(realtimeStatus)}
            size="small"
            sx={{
              backgroundColor: getStatusColor(realtimeStatus),
              color: 'white',
              fontSize: '0.75rem'
            }}
          />
        </Box>
      </Box>

      <Divider />

      {/* قائمة العناصر */}
      <List sx={{ flexGrow: 1, py: 1 }}>
        {menuItems.map((item) => (
          <ListItem key={item.id} disablePadding sx={{ mb: 0.5 }}>
            <ListItemButton
              selected={selectedSection === item.id}
              onClick={() => handleSectionChange(item.id)}
              sx={{
                mx: 1,
                borderRadius: 2,
                '&.Mui-selected': {
                  backgroundColor: `${item.color}20`,
                  borderLeft: `4px solid ${item.color}`,
                  '&:hover': {
                    backgroundColor: `${item.color}30`,
                  },
                },
                '&:hover': {
                  backgroundColor: `${item.color}10`,
                },
              }}
            >
              <ListItemIcon sx={{ color: item.color, minWidth: 40 }}>
                {item.icon}
              </ListItemIcon>
              <ListItemText
                primary={item.label}
                secondary={item.description}
                primaryTypographyProps={{
                  fontSize: '0.9rem',
                  fontWeight: selectedSection === item.id ? 'bold' : 'normal'
                }}
                secondaryTypographyProps={{
                  fontSize: '0.75rem',
                  color: 'text.secondary'
                }}
              />
            </ListItemButton>
          </ListItem>
        ))}
      </List>

      <Divider />

      {/* معلومات المستخدم */}
      <Box sx={{ p: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Avatar sx={{ width: 32, height: 32, mr: 1, bgcolor: '#FFD700' }}>
            {user?.name?.charAt(0) || 'A'}
          </Avatar>
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
              {user?.name || 'المدير'}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              مدير النظام
            </Typography>
          </Box>
        </Box>

        <ListItemButton
          onClick={handleLogout}
          sx={{
            borderRadius: 1,
            color: '#F44336',
            '&:hover': {
              backgroundColor: '#********',
            },
          }}
        >
          <ListItemIcon sx={{ color: '#F44336', minWidth: 40 }}>
            <Logout />
          </ListItemIcon>
          <ListItemText primary="تسجيل الخروج" />
        </ListItemButton>
      </Box>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh', backgroundColor: '#f5f5f5' }}>
      {/* شريط التطبيق العلوي */}
      <AppBar
        position="fixed"
        sx={{
          width: { md: `calc(100% - ${drawerWidth}px)` },
          mr: { md: `${drawerWidth}px` },
          backgroundColor: '#0000FF',
          boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
        }}
      >
        <Toolbar sx={{ direction: 'rtl' }}>
          {/* أيقونة الإشعارات */}
          <IconButton color="inherit">
            <Badge badgeContent={0} color="error">
              <Notifications />
            </Badge>
          </IconButton>

          {/* مؤشر حالة النظام */}
          <Tooltip title={`حالة النظام: ${getStatusText(systemStatus)}`}>
            <Chip
              icon={systemStatus === 'connected' ? <CheckCircle /> :
                    systemStatus === 'error' ? <ErrorIcon /> : <Sync />}
              label={getStatusText(systemStatus)}
              size="small"
              sx={{
                backgroundColor: getStatusColor(systemStatus),
                color: 'white',
                ml: 2
              }}
            />
          </Tooltip>

          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1, textAlign: 'right' }}>
            {menuItems.find(item => item.id === selectedSection)?.label || 'لوحة التحكم'}
          </Typography>

          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { md: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
        </Toolbar>
      </AppBar>

      {/* القائمة الجانبية */}
      <Box
        component="nav"
        sx={{ width: { md: drawerWidth }, flexShrink: { md: 0 } }}
      >
        <Drawer
          variant="temporary"
          anchor="right"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true,
          }}
          sx={{
            display: { xs: 'block', md: 'none' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
              direction: 'rtl'
            },
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          anchor="right"
          sx={{
            display: { xs: 'none', md: 'block' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
              direction: 'rtl'
            },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>

      {/* المحتوى الرئيسي */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          width: { md: `calc(100% - ${drawerWidth}px)` },
          minHeight: '100vh',
          backgroundColor: '#f8f9fa',
          direction: 'rtl'
        }}
      >
        <Toolbar />
        <Container maxWidth="xl" sx={{ py: 3 }}>
          {renderContent()}
        </Container>
      </Box>
    </Box>
  );
};

export default AdminDashboard;
