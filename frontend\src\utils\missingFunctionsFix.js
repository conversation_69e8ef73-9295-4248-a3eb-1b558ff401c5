/**
 * إصلاح الدوال المفقودة - SKILLS WORLD ACADEMY
 * Missing Functions Fix for Console Errors
 */

// إصلاح دوال Firebase المفقودة
export const mockFirebaseFunctions = {
  // دوال Firestore
  collection: (db, path) => ({
    id: path,
    path: path,
    parent: null,
    firestore: db
  }),
  
  doc: (db, path, ...segments) => ({
    id: segments[segments.length - 1] || 'mock-doc',
    path: [path, ...segments].join('/'),
    parent: null,
    firestore: db
  }),
  
  addDoc: async (collection, data) => {
    console.log('Mock addDoc:', collection.path, data);
    return {
      id: 'mock-' + Date.now(),
      path: collection.path + '/mock-' + Date.now()
    };
  },
  
  updateDoc: async (docRef, data) => {
    console.log('Mock updateDoc:', docRef.path, data);
    return true;
  },
  
  deleteDoc: async (docRef) => {
    console.log('Mock deleteDoc:', docRef.path);
    return true;
  },
  
  getDocs: async (query) => {
    console.log('Mock getDocs:', query);
    return {
      empty: true,
      size: 0,
      docs: [],
      forEach: () => {}
    };
  },
  
  getDoc: async (docRef) => {
    console.log('Mock getDoc:', docRef.path);
    return {
      exists: () => false,
      data: () => null,
      id: docRef.id,
      ref: docRef
    };
  },
  
  onSnapshot: (query, callback) => {
    console.log('Mock onSnapshot:', query);
    // إرجاع دالة إلغاء الاشتراك
    return () => console.log('Mock unsubscribe');
  },
  
  query: (collection, ...constraints) => ({
    collection: collection,
    constraints: constraints,
    type: 'query'
  }),
  
  where: (field, operator, value) => ({
    type: 'where',
    field: field,
    operator: operator,
    value: value
  }),
  
  orderBy: (field, direction = 'asc') => ({
    type: 'orderBy',
    field: field,
    direction: direction
  }),
  
  limit: (count) => ({
    type: 'limit',
    count: count
  }),
  
  serverTimestamp: () => ({
    type: 'serverTimestamp',
    value: new Date()
  }),
  
  increment: (value) => ({
    type: 'increment',
    value: value
  }),
  
  // دوال Auth
  signInWithEmailAndPassword: async (auth, email, password) => {
    console.log('Mock signInWithEmailAndPassword:', email);
    return {
      user: {
        uid: 'mock-uid',
        email: email,
        displayName: 'Mock User'
      }
    };
  },
  
  createUserWithEmailAndPassword: async (auth, email, password) => {
    console.log('Mock createUserWithEmailAndPassword:', email);
    return {
      user: {
        uid: 'mock-uid-' + Date.now(),
        email: email,
        displayName: 'New Mock User'
      }
    };
  },
  
  signOut: async (auth) => {
    console.log('Mock signOut');
    return true;
  },
  
  onAuthStateChanged: (auth, callback) => {
    console.log('Mock onAuthStateChanged');
    // إرجاع دالة إلغاء الاشتراك
    return () => console.log('Mock auth unsubscribe');
  }
};

// إصلاح دوال Supabase المفقودة
export const mockSupabaseFunctions = {
  from: (table) => ({
    select: (columns = '*') => ({
      eq: (column, value) => ({
        single: () => ({ data: null, error: null }),
        limit: (count) => ({ data: [], error: null }),
        order: (column, options) => ({ data: [], error: null }),
        data: [],
        error: null
      }),
      data: [],
      error: null
    }),
    
    insert: (data) => ({
      select: () => ({
        single: () => ({ data: null, error: null }),
        data: [],
        error: null
      }),
      data: null,
      error: null
    }),
    
    update: (data) => ({
      eq: (column, value) => ({
        select: () => ({
          single: () => ({ data: null, error: null }),
          data: [],
          error: null
        }),
        data: null,
        error: null
      })
    }),
    
    delete: () => ({
      eq: (column, value) => ({
        data: null,
        error: null
      })
    }),
    
    rpc: (functionName, params) => ({
      data: null,
      error: null
    })
  }),
  
  auth: {
    signIn: async (credentials) => {
      console.log('Mock Supabase signIn');
      return { data: null, error: null };
    },
    
    signOut: async () => {
      console.log('Mock Supabase signOut');
      return { data: null, error: null };
    },
    
    onAuthStateChange: (callback) => {
      console.log('Mock Supabase onAuthStateChange');
      return () => console.log('Mock Supabase auth unsubscribe');
    }
  },
  
  channel: (name) => ({
    on: (event, filter, callback) => ({
      subscribe: () => console.log('Mock Supabase subscribe')
    })
  }),
  
  removeChannel: (channel) => {
    console.log('Mock Supabase removeChannel');
  },
  
  removeAllChannels: () => {
    console.log('Mock Supabase removeAllChannels');
  }
};

// إصلاح دوال React المفقودة
export const mockReactFunctions = {
  useState: (initialState) => {
    console.log('Mock useState:', initialState);
    return [initialState, () => {}];
  },
  
  useEffect: (effect, deps) => {
    console.log('Mock useEffect');
    try {
      effect();
    } catch (error) {
      console.warn('Mock useEffect error:', error);
    }
  },
  
  useContext: (context) => {
    console.log('Mock useContext');
    return {};
  },
  
  createContext: (defaultValue) => {
    console.log('Mock createContext');
    return {
      Provider: ({ children }) => children,
      Consumer: ({ children }) => children(defaultValue)
    };
  }
};

// دالة تطبيق جميع إصلاحات الدوال
export const applyFunctionFixes = () => {
  try {
    // تطبيق الإصلاحات على النطاق العام
    if (typeof window !== 'undefined') {
      // Firebase functions
      Object.assign(window, mockFirebaseFunctions);
      
      // Supabase functions
      if (!window.supabase) {
        window.supabase = mockSupabaseFunctions;
      }
      
      // React functions
      if (!window.React) {
        window.React = mockReactFunctions;
      }
      
      // إضافة دوال مساعدة
      window.mockFirebaseFunctions = mockFirebaseFunctions;
      window.mockSupabaseFunctions = mockSupabaseFunctions;
      window.mockReactFunctions = mockReactFunctions;
    }

    console.log('✅ تم تطبيق جميع إصلاحات الدوال المفقودة بنجاح');
    return true;
  } catch (error) {
    console.error('❌ خطأ في تطبيق إصلاحات الدوال:', error);
    return false;
  }
};

// دالة التحقق من وجود الدوال المطلوبة
export const checkRequiredFunctions = () => {
  const requiredFunctions = [
    'collection', 'doc', 'addDoc', 'updateDoc', 'deleteDoc',
    'getDocs', 'getDoc', 'onSnapshot', 'query', 'where',
    'orderBy', 'limit', 'serverTimestamp', 'increment'
  ];
  
  const missingFunctions = [];
  
  requiredFunctions.forEach(funcName => {
    if (typeof window[funcName] === 'undefined') {
      missingFunctions.push(funcName);
    }
  });
  
  if (missingFunctions.length > 0) {
    console.warn('⚠️ دوال مفقودة:', missingFunctions);
    console.log('💡 سيتم استخدام الدوال البديلة');
  } else {
    console.log('✅ جميع الدوال المطلوبة متاحة');
  }
  
  return {
    allPresent: missingFunctions.length === 0,
    missing: missingFunctions
  };
};

// تطبيق الإصلاحات تلقائياً عند تحميل الملف
applyFunctionFixes();

// التحقق من الدوال المطلوبة
const functionCheck = checkRequiredFunctions();

// تصدير جميع الأدوات
export {
  mockFirebaseFunctions,
  mockSupabaseFunctions,
  mockReactFunctions,
  checkRequiredFunctions
};

export default {
  applyFunctionFixes,
  checkRequiredFunctions,
  mockFirebaseFunctions,
  mockSupabaseFunctions,
  mockReactFunctions,
  functionCheck
};
