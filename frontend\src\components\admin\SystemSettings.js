import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Alert,
  useTheme,
  useMediaQuery,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  ExpandMore,
  CheckCircle,
  Sync,
  DeleteSweep,
  Add,
  Settings,
  BugReport,
  Storage,
  Speed,
  Security,
  Refresh,
  PlayArrow,
  Stop
} from '@mui/icons-material';

// استيراد مكونات الاختبار
import SystemTestDashboard from './SystemTestDashboard';
import SupabaseConnectionTest from '../SupabaseConnectionTest';
import RealTimeSyncTest from './RealTimeSyncTest';
import DatabaseCleanerTool from './DatabaseCleanerTool';
import AddFunctionsTest from './AddFunctionsTest';

const SystemSettings = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.between('md', 'lg'));
  const isSmallMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const [expandedPanel, setExpandedPanel] = useState(false);
  const [testResults, setTestResults] = useState({});
  const [loading, setLoading] = useState({});

  const handlePanelChange = (panel) => (event, isExpanded) => {
    setExpandedPanel(isExpanded ? panel : false);
  };

  const systemTools = [
    {
      id: 'system-test',
      title: 'اختبار النظام',
      description: 'اختبار التكامل والأداء العام للنظام',
      icon: <CheckCircle />,
      color: '#E91E63',
      component: SystemTestDashboard,
      category: 'testing'
    },
    {
      id: 'supabase-test',
      title: 'اختبار Supabase',
      description: 'اختبار اتصال قاعدة البيانات والوظائف',
      icon: <Storage />,
      color: '#00C851',
      component: SupabaseConnectionTest,
      category: 'database'
    },
    {
      id: 'realtime-sync-test',
      title: 'اختبار التزامن الفوري',
      description: 'اختبار التزامن بين الإدارة والطلاب',
      icon: <Sync />,
      color: '#FF5722',
      component: RealTimeSyncTest,
      category: 'sync'
    },
    {
      id: 'database-cleaner',
      title: 'تنظيف قاعدة البيانات',
      description: 'إزالة البيانات الوهمية وتنظيف قاعدة البيانات',
      icon: <DeleteSweep />,
      color: '#E91E63',
      component: DatabaseCleanerTool,
      category: 'maintenance'
    },
    {
      id: 'add-functions-test',
      title: 'اختبار وظائف الإضافة',
      description: 'اختبار إضافة الطلاب والكورسات والتسجيلات',
      icon: <Add />,
      color: '#4CAF50',
      component: AddFunctionsTest,
      category: 'testing'
    }
  ];

  const categories = {
    testing: { label: 'الاختبارات', icon: <BugReport />, color: '#E91E63' },
    database: { label: 'قاعدة البيانات', icon: <Storage />, color: '#00C851' },
    sync: { label: 'التزامن', icon: <Sync />, color: '#FF5722' },
    maintenance: { label: 'الصيانة', icon: <Settings />, color: '#795548' }
  };

  const runQuickTest = async (toolId) => {
    setLoading(prev => ({ ...prev, [toolId]: true }));
    try {
      // محاكاة اختبار سريع
      await new Promise(resolve => setTimeout(resolve, 2000));
      setTestResults(prev => ({
        ...prev,
        [toolId]: {
          status: 'success',
          message: 'تم الاختبار بنجاح',
          timestamp: new Date().toLocaleTimeString('ar-SA')
        }
      }));
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        [toolId]: {
          status: 'error',
          message: 'فشل في الاختبار',
          timestamp: new Date().toLocaleTimeString('ar-SA')
        }
      }));
    } finally {
      setLoading(prev => ({ ...prev, [toolId]: false }));
    }
  };

  const renderToolCard = (tool) => {
    const result = testResults[tool.id];
    const isLoading = loading[tool.id];

    return (
      <Card
        key={tool.id}
        sx={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          transition: 'all 0.3s ease',
          '&:hover': {
            transform: 'translateY(-4px)',
            boxShadow: 4
          },
          border: result?.status === 'success' ? '2px solid #4CAF50' :
                 result?.status === 'error' ? '2px solid #f44336' : '1px solid #e0e0e0'
        }}
      >
        <CardContent sx={{ flexGrow: 1, p: { xs: 2, sm: 3 } }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Box
              sx={{
                p: 1,
                borderRadius: 2,
                backgroundColor: `${tool.color}20`,
                color: tool.color,
                mr: 2
              }}
            >
              {tool.icon}
            </Box>
            <Box sx={{ flexGrow: 1 }}>
              <Typography
                variant="h6"
                sx={{
                  fontSize: { xs: '1rem', sm: '1.1rem', md: '1.25rem' },
                  fontWeight: 600,
                  mb: 0.5
                }}
              >
                {tool.title}
              </Typography>
              <Chip
                label={categories[tool.category].label}
                size="small"
                sx={{
                  backgroundColor: `${categories[tool.category].color}20`,
                  color: categories[tool.category].color,
                  fontSize: '0.75rem'
                }}
              />
            </Box>
          </Box>

          <Typography
            variant="body2"
            color="text.secondary"
            sx={{
              mb: 2,
              fontSize: { xs: '0.8rem', sm: '0.875rem' },
              lineHeight: 1.4
            }}
          >
            {tool.description}
          </Typography>

          {result && (
            <Alert
              severity={result.status === 'success' ? 'success' : 'error'}
              sx={{ mb: 2, fontSize: '0.8rem' }}
            >
              {result.message} - {result.timestamp}
            </Alert>
          )}

          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            <Button
              variant="outlined"
              size={isSmallMobile ? "small" : "medium"}
              startIcon={<PlayArrow />}
              onClick={() => runQuickTest(tool.id)}
              disabled={isLoading}
              sx={{
                flex: { xs: '1 1 100%', sm: '1 1 auto' },
                fontSize: { xs: '0.8rem', sm: '0.875rem' }
              }}
            >
              {isLoading ? 'جاري الاختبار...' : 'اختبار سريع'}
            </Button>
          </Box>
        </CardContent>
      </Card>
    );
  };

  return (
    <Box sx={{ p: { xs: 1, sm: 2, md: 3 } }}>
      {/* رأس الصفحة */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: { xs: 'flex-start', sm: 'center' },
          flexDirection: { xs: 'column', sm: 'row' },
          gap: { xs: 2, sm: 0 },
          mb: 4
        }}
      >
        <Box>
          <Typography
            variant="h4"
            component="h1"
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              fontSize: { xs: '1.5rem', sm: '1.8rem', md: '2.125rem' },
              fontWeight: { xs: 600, md: 700 },
              mb: 1,
              color: '#795548'
            }}
          >
            <Settings
              sx={{
                fontSize: { xs: '1.5rem', sm: '1.8rem', md: '2rem' },
                color: '#795548'
              }}
            />
            إعدادات النظام
          </Typography>
          <Typography
            variant="body1"
            color="text.secondary"
            sx={{ fontSize: { xs: '0.9rem', sm: '1rem' } }}
          >
            جميع أدوات الاختبار والصيانة والتشخيص في مكان واحد
          </Typography>
        </Box>

        <Button
          variant="contained"
          startIcon={<Refresh />}
          onClick={() => window.location.reload()}
          size={isMobile ? "medium" : "large"}
          sx={{
            alignSelf: { xs: 'stretch', sm: 'auto' },
            fontSize: { xs: '0.8rem', sm: '0.875rem', md: '1rem' },
            backgroundColor: '#795548',
            '&:hover': {
              backgroundColor: '#5d4037'
            }
          }}
        >
          تحديث الصفحة
        </Button>
      </Box>

      {/* إحصائيات سريعة */}
      <Box sx={{ mb: 4 }}>
        <Typography
          variant="h5"
          sx={{
            mb: 3,
            fontSize: { xs: '1.2rem', sm: '1.3rem', md: '1.5rem' },
            fontWeight: 600,
            color: '#795548'
          }}
        >
          أدوات النظام السريعة
        </Typography>

        <Grid container spacing={{ xs: 2, sm: 3 }}>
          {systemTools.map((tool) => (
            <Grid item xs={12} sm={6} lg={4} key={tool.id}>
              {renderToolCard(tool)}
            </Grid>
          ))}
        </Grid>
      </Box>

      {/* الأدوات التفصيلية */}
      <Typography
        variant="h5"
        sx={{
          mb: 3,
          fontSize: { xs: '1.2rem', sm: '1.3rem', md: '1.5rem' },
          fontWeight: 600,
          color: '#795548'
        }}
      >
        الأدوات التفصيلية والمتقدمة
      </Typography>

      <Alert
        severity="info"
        sx={{
          mb: 3,
          backgroundColor: '#e8f5e8',
          '& .MuiAlert-icon': {
            color: '#795548'
          }
        }}
      >
        <Typography variant="body2">
          استخدم هذه الأدوات لإجراء اختبارات شاملة وصيانة متقدمة للنظام.
          يُنصح بإجراء هذه الاختبارات بشكل دوري لضمان الأداء الأمثل.
        </Typography>
      </Alert>

      {systemTools.map((tool) => (
        <Accordion
          key={tool.id}
          expanded={expandedPanel === tool.id}
          onChange={handlePanelChange(tool.id)}
          sx={{
            mb: 2,
            '&:before': { display: 'none' },
            boxShadow: 1,
            borderRadius: 2,
            '&.Mui-expanded': {
              boxShadow: 3
            }
          }}
        >
          <AccordionSummary
            expandIcon={<ExpandMore />}
            sx={{
              backgroundColor: `${tool.color}08`,
              borderRadius: 2,
              '&.Mui-expanded': {
                borderBottomLeftRadius: 0,
                borderBottomRightRadius: 0
              },
              minHeight: { xs: 56, sm: 64 },
              px: { xs: 2, sm: 3 }
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
              <Box
                sx={{
                  p: 1,
                  borderRadius: 1,
                  backgroundColor: `${tool.color}20`,
                  color: tool.color,
                  mr: 2
                }}
              >
                {tool.icon}
              </Box>
              <Box sx={{ flexGrow: 1 }}>
                <Typography
                  variant="h6"
                  sx={{
                    fontSize: { xs: '1rem', sm: '1.1rem' },
                    fontWeight: 600
                  }}
                >
                  {tool.title}
                </Typography>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{
                    fontSize: { xs: '0.8rem', sm: '0.875rem' },
                    display: { xs: 'none', sm: 'block' }
                  }}
                >
                  {tool.description}
                </Typography>
              </Box>
            </Box>
          </AccordionSummary>
          <AccordionDetails sx={{ p: { xs: 2, sm: 3 } }}>
            <tool.component />
          </AccordionDetails>
        </Accordion>
      ))}
    </Box>
  );
};

export default SystemSettings;
