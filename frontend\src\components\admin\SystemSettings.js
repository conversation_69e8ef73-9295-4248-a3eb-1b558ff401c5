import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  <PERSON>rid,
  Button,
  Switch,
  FormControlLabel,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Divider,
  Alert,
  useTheme,
  useMediaQuery,
  Snackbar,
  CircularProgress
} from '@mui/material';
import {
  Settings,
  Security,
  Storage,
  Notifications,
  Language,
  Palette,
  Save,
  Refresh,
  Info,
  CheckCircle,
  Error as ErrorIcon
} from '@mui/icons-material';

const SystemSettings = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  // حالات الإعدادات
  const [settings, setSettings] = useState({
    // إعدادات عامة
    academyName: 'SKILLS WORLD ACADEMY',
    adminName: 'ALAA ABD HAMIED',
    adminEmail: 'ALAA <EMAIL>',
    adminPhone: '0506747770',
    
    // إعدادات الأمان
    enableTwoFactor: false,
    sessionTimeout: 30,
    maxLoginAttempts: 5,
    
    // إعدادات الإشعارات
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    
    // إعدادات اللغة والواجهة
    defaultLanguage: 'ar',
    rtlLayout: true,
    darkMode: false,
    
    // إعدادات التخزين
    maxFileSize: 50,
    allowedFileTypes: ['pdf', 'mp4', 'jpg', 'png'],
    autoBackup: true
  });
  
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [systemStatus, setSystemStatus] = useState({
    database: 'connected',
    storage: 'connected',
    notifications: 'active'
  });
  
  // تحميل الإعدادات عند بدء المكون
  useEffect(() => {
    loadSettings();
    checkSystemStatus();
  }, []);
  
  // تحميل الإعدادات من قاعدة البيانات
  const loadSettings = async () => {
    try {
      setLoading(true);
      console.log('📥 تحميل إعدادات النظام...');
      
      // محاكاة تحميل البيانات
      setTimeout(() => {
        setLoading(false);
        console.log('✅ تم تحميل الإعدادات بنجاح');
      }, 1000);
    } catch (error) {
      console.error('❌ خطأ في تحميل الإعدادات:', error);
      setLoading(false);
      showSnackbar('فشل في تحميل الإعدادات', 'error');
    }
  };
  
  // فحص حالة النظام
  const checkSystemStatus = async () => {
    try {
      // محاكاة فحص حالة النظام
      setTimeout(() => {
        setSystemStatus({
          database: Math.random() > 0.1 ? 'connected' : 'error',
          storage: Math.random() > 0.1 ? 'connected' : 'error',
          notifications: Math.random() > 0.1 ? 'active' : 'error'
        });
      }, 1500);
    } catch (error) {
      console.error('❌ خطأ في فحص حالة النظام:', error);
    }
  };
  
  // حفظ الإعدادات
  const saveSettings = async () => {
    try {
      setLoading(true);
      console.log('💾 حفظ إعدادات النظام...', settings);
      
      // محاكاة حفظ البيانات
      setTimeout(() => {
        setLoading(false);
        showSnackbar('تم حفظ الإعدادات بنجاح', 'success');
        console.log('✅ تم حفظ الإعدادات بنجاح');
      }, 1500);
    } catch (error) {
      console.error('❌ خطأ في حفظ الإعدادات:', error);
      setLoading(false);
      showSnackbar('فشل في حفظ الإعدادات', 'error');
    }
  };
  
  // إعادة تعيين الإعدادات للافتراضية
  const resetSettings = () => {
    setSettings({
      academyName: 'SKILLS WORLD ACADEMY',
      adminName: 'ALAA ABD HAMIED',
      adminEmail: 'ALAA <EMAIL>',
      adminPhone: '0506747770',
      enableTwoFactor: false,
      sessionTimeout: 30,
      maxLoginAttempts: 5,
      emailNotifications: true,
      smsNotifications: false,
      pushNotifications: true,
      defaultLanguage: 'ar',
      rtlLayout: true,
      darkMode: false,
      maxFileSize: 50,
      allowedFileTypes: ['pdf', 'mp4', 'jpg', 'png'],
      autoBackup: true
    });
    showSnackbar('تم إعادة تعيين الإعدادات للافتراضية', 'info');
  };
  
  // عرض رسالة
  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({ open: true, message, severity });
  };
  
  // إغلاق رسالة
  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };
  
  // تحديث إعداد واحد
  const updateSetting = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // عرض حالة النظام
  const renderSystemStatus = () => (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
          <Info color="primary" />
          حالة النظام
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={4}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {systemStatus.database === 'connected' ? 
                <CheckCircle color="success" /> : 
                <ErrorIcon color="error" />
              }
              <Typography variant="body2">
                قاعدة البيانات: {systemStatus.database === 'connected' ? 'متصلة' : 'خطأ'}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={4}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {systemStatus.storage === 'connected' ? 
                <CheckCircle color="success" /> : 
                <ErrorIcon color="error" />
              }
              <Typography variant="body2">
                التخزين: {systemStatus.storage === 'connected' ? 'متصل' : 'خطأ'}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={4}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {systemStatus.notifications === 'active' ? 
                <CheckCircle color="success" /> : 
                <ErrorIcon color="error" />
              }
              <Typography variant="body2">
                الإشعارات: {systemStatus.notifications === 'active' ? 'نشطة' : 'خطأ'}
              </Typography>
            </Box>
          </Grid>
        </Grid>
        <Button
          variant="outlined"
          size="small"
          onClick={checkSystemStatus}
          sx={{ mt: 2 }}
          startIcon={<Refresh />}
        >
          تحديث الحالة
        </Button>
      </CardContent>
    </Card>
  );

  return (
    <Box sx={{ p: { xs: 2, sm: 3, md: 4 } }}>
      {/* العنوان الرئيسي */}
      <Box
        sx={{
          display: 'flex',
          flexDirection: { xs: 'column', sm: 'row' },
          justifyContent: 'space-between',
          alignItems: { xs: 'stretch', sm: 'center' },
          gap: 2,
          mb: 4
        }}
      >
        <Box>
          <Typography
            variant="h4"
            component="h1"
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              fontSize: { xs: '1.5rem', sm: '1.8rem', md: '2.125rem' },
              fontWeight: { xs: 600, md: 700 },
              mb: 1,
              color: '#795548'
            }}
          >
            <Settings
              sx={{
                fontSize: { xs: '1.5rem', sm: '1.8rem', md: '2rem' },
                color: '#795548'
              }}
            />
            إعدادات النظام
          </Typography>
          <Typography
            variant="body1"
            color="text.secondary"
            sx={{ fontSize: { xs: '0.9rem', sm: '1rem' } }}
          >
            إدارة إعدادات النظام والتكوين العام للأكاديمية
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', gap: 1, flexDirection: { xs: 'column', sm: 'row' } }}>
          <Button
            variant="outlined"
            onClick={resetSettings}
            startIcon={<Refresh />}
            size={isMobile ? "medium" : "large"}
          >
            إعادة تعيين
          </Button>
          <Button
            variant="contained"
            onClick={saveSettings}
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : <Save />}
            size={isMobile ? "medium" : "large"}
            sx={{
              backgroundColor: '#795548',
              '&:hover': {
                backgroundColor: '#5d4037'
              }
            }}
          >
            {loading ? 'جاري الحفظ...' : 'حفظ الإعدادات'}
          </Button>
        </Box>
      </Box>

      {/* حالة النظام */}
      {renderSystemStatus()}

      {/* تنبيه */}
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          هذه الصفحة تحتوي على إعدادات النظام الأساسية. تأكد من حفظ التغييرات قبل مغادرة الصفحة.
        </Typography>
      </Alert>

      {/* الإعدادات العامة */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
            <Settings color="primary" />
            الإعدادات العامة
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="اسم الأكاديمية"
                value={settings.academyName}
                onChange={(e) => updateSetting('academyName', e.target.value)}
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="اسم المدير"
                value={settings.adminName}
                onChange={(e) => updateSetting('adminName', e.target.value)}
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="البريد الإلكتروني"
                type="email"
                value={settings.adminEmail}
                onChange={(e) => updateSetting('adminEmail', e.target.value)}
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="رقم الهاتف"
                value={settings.adminPhone}
                onChange={(e) => updateSetting('adminPhone', e.target.value)}
                variant="outlined"
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* إعدادات الأمان */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
            <Security color="primary" />
            إعدادات الأمان
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.enableTwoFactor}
                    onChange={(e) => updateSetting('enableTwoFactor', e.target.checked)}
                  />
                }
                label="تفعيل المصادقة الثنائية"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="مهلة انتهاء الجلسة (دقيقة)"
                type="number"
                value={settings.sessionTimeout}
                onChange={(e) => updateSetting('sessionTimeout', parseInt(e.target.value))}
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="عدد محاولات تسجيل الدخول المسموحة"
                type="number"
                value={settings.maxLoginAttempts}
                onChange={(e) => updateSetting('maxLoginAttempts', parseInt(e.target.value))}
                variant="outlined"
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* إعدادات الإشعارات */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
            <Notifications color="primary" />
            إعدادات الإشعارات
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.emailNotifications}
                    onChange={(e) => updateSetting('emailNotifications', e.target.checked)}
                  />
                }
                label="إشعارات البريد الإلكتروني"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.smsNotifications}
                    onChange={(e) => updateSetting('smsNotifications', e.target.checked)}
                  />
                }
                label="إشعارات الرسائل النصية"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.pushNotifications}
                    onChange={(e) => updateSetting('pushNotifications', e.target.checked)}
                  />
                }
                label="الإشعارات الفورية"
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* إعدادات اللغة والواجهة */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
            <Language color="primary" />
            إعدادات اللغة والواجهة
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>اللغة الافتراضية</InputLabel>
                <Select
                  value={settings.defaultLanguage}
                  onChange={(e) => updateSetting('defaultLanguage', e.target.value)}
                  label="اللغة الافتراضية"
                >
                  <MenuItem value="ar">العربية</MenuItem>
                  <MenuItem value="en">English</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.rtlLayout}
                    onChange={(e) => updateSetting('rtlLayout', e.target.checked)}
                  />
                }
                label="تخطيط من اليمين لليسار"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.darkMode}
                    onChange={(e) => updateSetting('darkMode', e.target.checked)}
                  />
                }
                label="الوضع المظلم"
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* إعدادات التخزين */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
            <Storage color="primary" />
            إعدادات التخزين
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="الحد الأقصى لحجم الملف (MB)"
                type="number"
                value={settings.maxFileSize}
                onChange={(e) => updateSetting('maxFileSize', parseInt(e.target.value))}
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.autoBackup}
                    onChange={(e) => updateSetting('autoBackup', e.target.checked)}
                  />
                }
                label="النسخ الاحتياطي التلقائي"
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* رسالة التنبيه */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default SystemSettings;
