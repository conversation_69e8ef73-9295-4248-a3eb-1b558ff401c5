/**
 * إصلاح متغيرات البيئة - SKILLS WORLD ACADEMY
 * Environment Variables Fix for Console Errors
 */

// إعدادات Firebase الافتراضية
const defaultFirebaseConfig = {
  apiKey: "AIzaSyBXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
  authDomain: "marketwise-academy-qhizq.firebaseapp.com",
  projectId: "marketwise-academy-qhizq",
  storageBucket: "marketwise-academy-qhizq.appspot.com",
  messagingSenderId: "123456789012",
  appId: "1:123456789012:web:abcdefghijklmnop"
};

// إعدادات Supabase الافتراضية
const defaultSupabaseConfig = {
  url: "https://auwpeiicfwcysoexoogf.supabase.co",
  anonKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF1d3BlaWljZndjeXNvZXhvb2dmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIxNzA3MzAsImV4cCI6MjA2Nzc0NjczMH0.3twZ1c6M2EyBoBe66PjmwsUwvzK0nnV89Bt3yLcGBjY"
};

// دالة الحصول على متغير البيئة مع قيمة افتراضية
const getEnvVar = (key, defaultValue = '') => {
  try {
    // محاولة الحصول على المتغير من process.env
    if (typeof process !== 'undefined' && process.env && process.env[key]) {
      return process.env[key];
    }
    
    // محاولة الحصول على المتغير من window
    if (typeof window !== 'undefined' && window.process && window.process.env && window.process.env[key]) {
      return window.process.env[key];
    }
    
    // إرجاع القيمة الافتراضية
    return defaultValue;
  } catch (error) {
    console.warn(`خطأ في الحصول على متغير البيئة ${key}:`, error);
    return defaultValue;
  }
};

// إنشاء كائن process.env آمن
const safeProcessEnv = {
  NODE_ENV: getEnvVar('NODE_ENV', 'development'),
  
  // متغيرات Firebase
  REACT_APP_FIREBASE_API_KEY: getEnvVar('REACT_APP_FIREBASE_API_KEY', defaultFirebaseConfig.apiKey),
  REACT_APP_FIREBASE_AUTH_DOMAIN: getEnvVar('REACT_APP_FIREBASE_AUTH_DOMAIN', defaultFirebaseConfig.authDomain),
  REACT_APP_FIREBASE_PROJECT_ID: getEnvVar('REACT_APP_FIREBASE_PROJECT_ID', defaultFirebaseConfig.projectId),
  REACT_APP_FIREBASE_STORAGE_BUCKET: getEnvVar('REACT_APP_FIREBASE_STORAGE_BUCKET', defaultFirebaseConfig.storageBucket),
  REACT_APP_FIREBASE_MESSAGING_SENDER_ID: getEnvVar('REACT_APP_FIREBASE_MESSAGING_SENDER_ID', defaultFirebaseConfig.messagingSenderId),
  REACT_APP_FIREBASE_APP_ID: getEnvVar('REACT_APP_FIREBASE_APP_ID', defaultFirebaseConfig.appId),
  
  // متغيرات Supabase
  REACT_APP_SUPABASE_URL: getEnvVar('REACT_APP_SUPABASE_URL', defaultSupabaseConfig.url),
  REACT_APP_SUPABASE_ANON_KEY: getEnvVar('REACT_APP_SUPABASE_ANON_KEY', defaultSupabaseConfig.anonKey),
  
  // متغيرات أخرى
  REACT_APP_API_URL: getEnvVar('REACT_APP_API_URL', 'http://localhost:5000'),
  REACT_APP_VERSION: getEnvVar('REACT_APP_VERSION', '3.0.0'),
  REACT_APP_ACADEMY_NAME: getEnvVar('REACT_APP_ACADEMY_NAME', 'SKILLS WORLD ACADEMY')
};

// دالة إنشاء كائن process آمن
const createSafeProcess = () => {
  return {
    env: safeProcessEnv,
    platform: typeof process !== 'undefined' ? process.platform : 'browser',
    version: typeof process !== 'undefined' ? process.version : 'unknown',
    versions: typeof process !== 'undefined' ? process.versions : {},
    argv: typeof process !== 'undefined' ? process.argv : [],
    cwd: typeof process !== 'undefined' && process.cwd ? process.cwd : () => '/',
    exit: typeof process !== 'undefined' && process.exit ? process.exit : () => {}
  };
};

// دالة تطبيق إصلاحات البيئة
export const applyEnvironmentFixes = () => {
  try {
    // إنشاء كائن process آمن إذا لم يكن موجوداً
    if (typeof process === 'undefined') {
      if (typeof window !== 'undefined') {
        window.process = createSafeProcess();
      }
      if (typeof global !== 'undefined') {
        global.process = createSafeProcess();
      }
    } else {
      // تحديث process.env الموجود
      if (process.env) {
        Object.assign(process.env, safeProcessEnv);
      } else {
        process.env = safeProcessEnv;
      }
    }

    // إضافة المتغيرات للنطاق العام
    if (typeof window !== 'undefined') {
      window.ENV = safeProcessEnv;
      window.getEnvVar = getEnvVar;
      window.defaultFirebaseConfig = defaultFirebaseConfig;
      window.defaultSupabaseConfig = defaultSupabaseConfig;
    }

    console.log('✅ تم تطبيق إصلاحات متغيرات البيئة بنجاح');
    console.log('🔧 متغيرات البيئة المتاحة:', Object.keys(safeProcessEnv));
    
    return true;
  } catch (error) {
    console.error('❌ خطأ في تطبيق إصلاحات البيئة:', error);
    return false;
  }
};

// دالة التحقق من صحة الإعدادات
export const validateEnvironment = () => {
  const issues = [];
  
  // التحقق من Firebase
  if (!safeProcessEnv.REACT_APP_FIREBASE_API_KEY || safeProcessEnv.REACT_APP_FIREBASE_API_KEY.includes('XXX')) {
    issues.push('Firebase API Key غير صحيح');
  }
  
  if (!safeProcessEnv.REACT_APP_FIREBASE_PROJECT_ID) {
    issues.push('Firebase Project ID مفقود');
  }
  
  // التحقق من Supabase
  if (!safeProcessEnv.REACT_APP_SUPABASE_URL || !safeProcessEnv.REACT_APP_SUPABASE_URL.includes('supabase.co')) {
    issues.push('Supabase URL غير صحيح');
  }
  
  if (!safeProcessEnv.REACT_APP_SUPABASE_ANON_KEY) {
    issues.push('Supabase Anon Key مفقود');
  }
  
  if (issues.length > 0) {
    console.warn('⚠️ مشاكل في متغيرات البيئة:', issues);
    console.log('💡 سيتم استخدام القيم الافتراضية للتطوير');
  } else {
    console.log('✅ جميع متغيرات البيئة صحيحة');
  }
  
  return {
    isValid: issues.length === 0,
    issues: issues,
    env: safeProcessEnv
  };
};

// تطبيق الإصلاحات تلقائياً عند تحميل الملف
applyEnvironmentFixes();

// التحقق من صحة البيئة
const validation = validateEnvironment();

// تصدير جميع الأدوات
export {
  safeProcessEnv,
  getEnvVar,
  createSafeProcess,
  defaultFirebaseConfig,
  defaultSupabaseConfig,
  validateEnvironment
};

export default {
  applyEnvironmentFixes,
  validateEnvironment,
  safeProcessEnv,
  getEnvVar,
  createSafeProcess,
  defaultFirebaseConfig,
  defaultSupabaseConfig,
  validation
};
