/**
 * معالجة الأخطاء الشاملة - SKILLS WORLD ACADEMY
 * Comprehensive Error Handling for Console Errors
 */

// مستويات الأخطاء
const ERROR_LEVELS = {
  INFO: 'info',
  WARN: 'warn',
  ERROR: 'error',
  CRITICAL: 'critical'
};

// أنواع الأخطاء
const ERROR_TYPES = {
  NETWORK: 'network',
  VALIDATION: 'validation',
  AUTHENTICATION: 'authentication',
  PERMISSION: 'permission',
  SYSTEM: 'system',
  UI: 'ui',
  DATABASE: 'database'
};

// مجموعة الأخطاء المسجلة
let errorLog = [];
let errorHandlers = new Map();

// دالة تسجيل الأخطاء
const logError = (error, context = {}) => {
  const errorEntry = {
    id: Date.now() + Math.random(),
    timestamp: new Date().toISOString(),
    message: error.message || error.toString(),
    stack: error.stack,
    level: context.level || ERROR_LEVELS.ERROR,
    type: context.type || ERROR_TYPES.SYSTEM,
    context: context,
    url: window.location?.href,
    userAgent: navigator?.userAgent
  };

  errorLog.push(errorEntry);

  // الاحتفاظ بآخر 100 خطأ فقط
  if (errorLog.length > 100) {
    errorLog = errorLog.slice(-100);
  }

  // طباعة الخطأ في وحدة التحكم
  const logMethod = console[errorEntry.level] || console.error;
  logMethod(`[${errorEntry.level.toUpperCase()}] ${errorEntry.message}`, errorEntry);

  return errorEntry;
};

// دالة معالجة الأخطاء العامة
const handleError = (error, context = {}) => {
  try {
    // تسجيل الخطأ
    const errorEntry = logError(error, context);

    // تشغيل معالجات الأخطاء المخصصة
    const handler = errorHandlers.get(context.type) || errorHandlers.get('default');
    if (handler && typeof handler === 'function') {
      handler(errorEntry);
    }

    // إرسال الخطأ للخدمة الخارجية (اختياري)
    if (context.sendToService !== false) {
      sendErrorToService(errorEntry);
    }

    return errorEntry;
  } catch (handlingError) {
    console.error('خطأ في معالجة الخطأ:', handlingError);
  }
};

// دالة إرسال الأخطاء للخدمة الخارجية
const sendErrorToService = async (errorEntry) => {
  try {
    // في بيئة الإنتاج، يمكن إرسال الأخطاء لخدمة مثل Sentry
    if (process.env.NODE_ENV === 'production') {
      // await fetch('/api/errors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(errorEntry)
      // });
    }
  } catch (error) {
    console.warn('فشل في إرسال الخطأ للخدمة:', error);
  }
};

// دالة تسجيل معالج خطأ مخصص
const registerErrorHandler = (type, handler) => {
  if (typeof handler === 'function') {
    errorHandlers.set(type, handler);
    console.log(`✅ تم تسجيل معالج الخطأ لنوع: ${type}`);
  } else {
    console.warn(`⚠️ معالج الخطأ يجب أن يكون دالة: ${type}`);
  }
};

// معالجات الأخطاء الافتراضية
const defaultErrorHandlers = {
  network: (errorEntry) => {
    console.warn('🌐 خطأ في الشبكة:', errorEntry.message);
    // يمكن إضافة منطق إعادة المحاولة هنا
  },

  validation: (errorEntry) => {
    console.warn('✅ خطأ في التحقق:', errorEntry.message);
    // يمكن إضافة منطق عرض رسائل التحقق هنا
  },

  authentication: (errorEntry) => {
    console.warn('🔐 خطأ في المصادقة:', errorEntry.message);
    // يمكن إضافة منطق إعادة التوجيه لتسجيل الدخول هنا
  },

  database: (errorEntry) => {
    console.warn('🗄️ خطأ في قاعدة البيانات:', errorEntry.message);
    // يمكن إضافة منطق إعادة الاتصال هنا
  },

  default: (errorEntry) => {
    console.error('❌ خطأ عام:', errorEntry.message);
  }
};

// دالة معالجة الأخطاء غير المتوقعة
const handleUnhandledErrors = () => {
  // معالجة الأخطاء غير المتوقعة في JavaScript
  window.addEventListener('error', (event) => {
    handleError(event.error || new Error(event.message), {
      type: ERROR_TYPES.SYSTEM,
      level: ERROR_LEVELS.ERROR,
      context: {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      }
    });
  });

  // معالجة الأخطاء في Promise
  window.addEventListener('unhandledrejection', (event) => {
    handleError(event.reason || new Error('Unhandled Promise Rejection'), {
      type: ERROR_TYPES.SYSTEM,
      level: ERROR_LEVELS.ERROR,
      context: {
        promise: event.promise
      }
    });
  });

  console.log('✅ تم تفعيل معالجة الأخطاء غير المتوقعة');
};

// دالة الحصول على إحصائيات الأخطاء
const getErrorStats = () => {
  const stats = {
    total: errorLog.length,
    byLevel: {},
    byType: {},
    recent: errorLog.slice(-10)
  };

  errorLog.forEach(error => {
    stats.byLevel[error.level] = (stats.byLevel[error.level] || 0) + 1;
    stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
  });

  return stats;
};

// دالة تنظيف سجل الأخطاء
const clearErrorLog = () => {
  const count = errorLog.length;
  errorLog = [];
  console.log(`🧹 تم تنظيف ${count} خطأ من السجل`);
  return count;
};

// دالة تطبيق معالجة الأخطاء
export const applyErrorHandling = () => {
  try {
    // تسجيل المعالجات الافتراضية
    Object.entries(defaultErrorHandlers).forEach(([type, handler]) => {
      registerErrorHandler(type, handler);
    });

    // تفعيل معالجة الأخطاء غير المتوقعة
    handleUnhandledErrors();

    // إضافة الأدوات للنطاق العام
    if (typeof window !== 'undefined') {
      window.errorHandling = {
        handleError,
        logError,
        registerErrorHandler,
        getErrorStats,
        clearErrorLog,
        ERROR_LEVELS,
        ERROR_TYPES
      };
    }

    console.log('✅ تم تطبيق معالجة الأخطاء الشاملة بنجاح');
    return true;
  } catch (error) {
    console.error('❌ خطأ في تطبيق معالجة الأخطاء:', error);
    return false;
  }
};

// دالة اختبار معالجة الأخطاء
export const testErrorHandling = () => {
  try {
    console.log('🧪 اختبار معالجة الأخطاء...');

    // اختبار أنواع مختلفة من الأخطاء
    handleError(new Error('اختبار خطأ الشبكة'), { type: ERROR_TYPES.NETWORK });
    handleError(new Error('اختبار خطأ التحقق'), { type: ERROR_TYPES.VALIDATION });
    handleError(new Error('اختبار خطأ عام'), { type: ERROR_TYPES.SYSTEM });

    const stats = getErrorStats();
    console.log('📊 إحصائيات الأخطاء بعد الاختبار:', stats);

    return {
      success: true,
      stats: stats
    };
  } catch (error) {
    console.error('❌ فشل في اختبار معالجة الأخطاء:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// تطبيق معالجة الأخطاء تلقائياً عند تحميل الملف
const handlingResult = applyErrorHandling();

// تصدير جميع الأدوات
export {
  handleError,
  logError,
  registerErrorHandler,
  getErrorStats,
  clearErrorLog,
  ERROR_LEVELS,
  ERROR_TYPES,
  defaultErrorHandlers
};

export default {
  applyErrorHandling,
  testErrorHandling,
  handleError,
  logError,
  registerErrorHandler,
  getErrorStats,
  clearErrorLog,
  ERROR_LEVELS,
  ERROR_TYPES,
  handlingResult
};
